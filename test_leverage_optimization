#!/bin/bash
# shellcheck disable=SC1091

# 测试动态杠杆调整优化脚本

cd "$(dirname "$0")" || exit
source .env
source /home/<USER>/freqtrade/.venv/bin/activate

echo "=== 测试杠杆优化配置 ==="

# 检查配置文件
CONFIG_FILE="user_data/hyperopt_configs/leverage_optimization.json"
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo "错误: 配置文件不存在: $CONFIG_FILE"
    exit 1
fi

echo "配置文件: $CONFIG_FILE ✓"

# 检查策略文件
STRATEGY_FILE="user_data/strategies/E0V1E_Enhanced.py"
if [[ ! -f "$STRATEGY_FILE" ]]; then
    echo "错误: 策略文件不存在: $STRATEGY_FILE"
    exit 1
fi

echo "策略文件: $STRATEGY_FILE ✓"

# 检查数据目录
if [[ ! -d "$DATA_DIR" ]]; then
    echo "警告: 数据目录不存在: $DATA_DIR"
    echo "请确保数据目录存在并包含必要的数据文件"
fi

echo "数据目录: $DATA_DIR"

# 测试配置文件语法
echo ""
echo "=== 测试配置文件语法 ==="
python -c "
import json
try:
    with open('$CONFIG_FILE', 'r') as f:
        config = json.load(f)
    print('配置文件语法正确 ✓')
    print(f'策略: {config.get(\"strategy\", \"未设置\")}')
    print(f'时间框架: {config.get(\"timeframe\", \"未设置\")}')
    print(f'交易对数量: {len(config.get(\"exchange\", {}).get(\"pair_whitelist\", []))}')
except Exception as e:
    print(f'配置文件语法错误: {e}')
    exit(1)
"

# 测试策略导入
echo ""
echo "=== 测试策略导入 ==="
python -c "
import sys
sys.path.append('user_data/strategies')
try:
    from E0V1E_Enhanced import E0V1E_Enhanced
    strategy = E0V1E_Enhanced()
    print('策略导入成功 ✓')
    
    # 检查杠杆相关参数
    leverage_params = [
        'base_leverage_low_vol',
        'base_leverage_normal', 
        'base_leverage_high_vol',
        'atr_low_threshold',
        'atr_high_threshold'
    ]
    
    missing_params = []
    for param in leverage_params:
        if not hasattr(strategy, param):
            missing_params.append(param)
    
    if missing_params:
        print(f'缺少杠杆参数: {missing_params}')
    else:
        print('杠杆参数检查通过 ✓')
        
except Exception as e:
    print(f'策略导入失败: {e}')
    exit(1)
"

# 测试freqtrade命令
echo ""
echo "=== 测试freqtrade命令 ==="

# 测试hyperopt命令语法
echo "测试hyperopt命令语法..."
freqtrade hyperopt --help > /dev/null 2>&1
if [[ $? -eq 0 ]]; then
    echo "hyperopt命令可用 ✓"
else
    echo "hyperopt命令不可用 ✗"
    exit 1
fi

# 测试hyperopt-list命令语法
echo "测试hyperopt-list命令语法..."
freqtrade hyperopt-list --help > /dev/null 2>&1
if [[ $? -eq 0 ]]; then
    echo "hyperopt-list命令可用 ✓"
else
    echo "hyperopt-list命令不可用 ✗"
    exit 1
fi

# 测试hyperopt-show命令语法
echo "测试hyperopt-show命令语法..."
freqtrade hyperopt-show --help > /dev/null 2>&1
if [[ $? -eq 0 ]]; then
    echo "hyperopt-show命令可用 ✓"
else
    echo "hyperopt-show命令不可用 ✗"
    exit 1
fi

echo ""
echo "=== 运行快速测试优化 ==="
echo "运行5轮测试优化以验证配置..."

# 运行快速测试
freqtrade hyperopt \
    --config "$CONFIG_FILE" \
    --datadir "$DATA_DIR" \
    --strategy "E0V1E_Enhanced" \
    --strategy-path "user_data/strategies" \
    --timerange "20250120-20250124" \
    --hyperopt-loss "SharpeHyperOptLoss" \
    --epochs 5 \
    --spaces "buy" \
    --hyperopt-min-trades 1 \
    --random-state 42 \
    --jobs 1

if [[ $? -eq 0 ]]; then
    echo ""
    echo "=== 测试成功 ==="
    echo "杠杆优化配置测试通过！"
    echo "现在可以运行完整的优化："
    echo "./optimize_leverage --epochs 100"
else
    echo ""
    echo "=== 测试失败 ==="
    echo "请检查配置和数据文件"
    exit 1
fi
