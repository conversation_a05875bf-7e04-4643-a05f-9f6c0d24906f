#!/bin/bash
# shellcheck disable=SC1091

# 显示帮助信息
show_help() {
    cat <<EOF
用法: $0 [选项]

动态杠杆调整超参数优化脚本

选项:
    -h, --help              显示此帮助信息
    --epochs NUM            优化轮数 (默认: 500)
    --min-trades NUM        最小交易数量 (默认: 10)
    --loss-function FUNC    损失函数 (默认: SharpeHyperOptLoss)
    --timerange RANGE       时间范围 (默认: 20250101-)
    --pairs LIST            交易对列表 (逗号分隔)
    --config-file FILE      配置文件 (默认: user_data/hyperopt_configs/leverage_optimization.json)
    --strategy NAME         策略名称 (默认: E0V1E_Enhanced)
    --data-dir DIR          数据目录 (覆盖默认设置)
    --spaces SPACES         优化空间 (默认: buy)
    --continue              继续之前的优化
    --random-state NUM      随机种子 (默认: 42)
    --jobs NUM              并行任务数 (默认: 1)
    --print-all             显示所有结果
    --print-json            JSON格式输出

示例:
    $0 --epochs 1000 --min-trades 20
    $0 --timerange 20240101-20241231 --pairs BTC/USDT,ETH/USDT
    $0 --loss-function CalmarHyperOptLoss --continue
    $0 --jobs 4 --print-all

可用的损失函数:
    - SharpeHyperOptLoss (默认): 最大化夏普比率
    - CalmarHyperOptLoss: 最大化卡尔玛比率
    - SortinoHyperOptLoss: 最大化索提诺比率
    - OnlyProfitHyperOptLoss: 仅关注利润
    - MaxDrawDownHyperOptLoss: 最小化最大回撤

EOF
}

# 切换到脚本目录
cd "$(dirname "$0")" || exit

# 加载 .env 文件中的默认值
source .env

# 默认参数
EPOCHS=500
MIN_TRADES=10
LOSS_FUNCTION="SharpeHyperOptLoss"
TIMERANGE="20250101-"
CONFIG_FILE="user_data/hyperopt_configs/leverage_optimization.json"
STRATEGY="E0V1E_Enhanced"
SPACES="buy"
CONTINUE_OPT=""
RANDOM_STATE=42
JOBS=1
PRINT_ALL=""

PRINT_JSON=""
PAIRS=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
    -h | --help)
        show_help
        exit 0
        ;;
    --epochs)
        EPOCHS="$2"
        shift 2
        ;;
    --min-trades)
        MIN_TRADES="$2"
        shift 2
        ;;
    --loss-function)
        LOSS_FUNCTION="$2"
        shift 2
        ;;
    --timerange)
        TIMERANGE="$2"
        shift 2
        ;;
    --pairs)
        PAIRS="$2"
        shift 2
        ;;
    --config-file)
        CONFIG_FILE="$2"
        shift 2
        ;;
    --strategy)
        STRATEGY="$2"
        shift 2
        ;;
    --data-dir)
        DATA_DIR="$2"
        shift 2
        ;;
    --spaces)
        SPACES="$2"
        shift 2
        ;;
    --continue)
        CONTINUE_OPT="--hyperopt-continue"
        shift
        ;;
    --random-state)
        RANDOM_STATE="$2"
        shift 2
        ;;
    --jobs)
        JOBS="$2"
        shift 2
        ;;
    --print-all)
        PRINT_ALL="--print-all"
        shift
        ;;
    --print-json)
        PRINT_JSON="--print-json"
        shift
        ;;
    *)
        echo "未知选项: $1"
        echo "使用 --help 查看可用选项"
        exit 1
        ;;
    esac
done

# 激活虚拟环境
source /home/<USER>/freqtrade/.venv/bin/activate

# 显示优化配置
echo "=== 动态杠杆调整超参数优化配置 ==="
echo "策略名称: ${STRATEGY}"
echo "配置文件: ${CONFIG_FILE}"
echo "数据目录: ${DATA_DIR}"
echo "时间范围: ${TIMERANGE}"
echo "优化轮数: ${EPOCHS}"
echo "最小交易数: ${MIN_TRADES}"
echo "损失函数: ${LOSS_FUNCTION}"
echo "优化空间: ${SPACES}"
echo "随机种子: ${RANDOM_STATE}"
echo "并行任务: ${JOBS}"
if [[ -n "$PAIRS" ]]; then
    echo "交易对: ${PAIRS}"
fi
if [[ -n "$CONTINUE_OPT" ]]; then
    echo "继续优化: 是"
fi
echo "=================================="

# 记录开始时间
start_time=$(date +"%Y%m%d-%H%M%S")
echo "开始优化: $start_time"

# 构建freqtrade hyperopt命令
hyperopt_cmd=(
    freqtrade hyperopt
    --config "${CONFIG_FILE}"
    --datadir "${DATA_DIR}"
    --strategy "${STRATEGY}"
    --strategy-path "user_data/strategies"
    --timerange "${TIMERANGE}"
    --hyperopt-loss "${LOSS_FUNCTION}"
    --epochs "${EPOCHS}"
    --spaces "${SPACES}"
    --hyperopt-min-trades "${MIN_TRADES}"
    --random-state "${RANDOM_STATE}"
    --jobs "${JOBS}"
)

# 添加可选参数
if [[ -n "$CONTINUE_OPT" ]]; then
    hyperopt_cmd+=("$CONTINUE_OPT")
fi

if [[ -n "$PAIRS" ]]; then
    hyperopt_cmd+=(--pairs "$PAIRS")
fi

if [[ -n "$PRINT_ALL" ]]; then
    hyperopt_cmd+=("$PRINT_ALL")
fi

if [[ -n "$PRINT_JSON" ]]; then
    hyperopt_cmd+=("$PRINT_JSON")
fi

# 执行超参数优化
echo "执行命令: ${hyperopt_cmd[*]}"
"${hyperopt_cmd[@]}"

# 记录结束时间
end_time=$(date +"%Y%m%d-%H%M%S")
echo "优化完成: $end_time"

# 显示最佳结果
echo ""
echo "=== 显示最佳优化结果 ==="
freqtrade hyperopt-list --best --config "${CONFIG_FILE}" --hyperopt-filename "hyperopt_results.pickle"

echo ""
echo "=== 显示最佳参数详情 ==="
freqtrade hyperopt-show --config "${CONFIG_FILE}" --hyperopt-filename "hyperopt_results.pickle" --best --print-json
