{"strategy": "E0V1E_Enhanced", "strategy_path": "user_data/strategies", "timeframe": "5m", "timerange": "20250120-20250124", "max_open_trades": 3, "stake_currency": "USDT", "stake_amount": 100, "dry_run": true, "datadir": "/mnt/d/data/binance", "trading_mode": "futures", "leverage_optimization_focus": {"description": "专注于动态杠杆调整参数的优化", "key_parameters": ["base_leverage_low_vol", "base_leverage_normal", "base_leverage_high_vol", "atr_low_threshold", "atr_high_threshold", "main_signal_leverage_multiplier", "alt_signal_leverage_multiplier", "rsi_extreme_threshold", "rsi_leverage_boost_factor", "volume_high_threshold", "volume_leverage_reduction_factor"], "optimization_goals": ["最大化夏普比率", "控制最大回撤", "提高盈利交易比例", "优化风险调整后收益"]}, "exchange": {"name": "binance", "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT"], "pair_blacklist": [], "ccxt_config": {}, "ccxt_async_config": {}, "skip_pair_validation": true, "markets_refresh_interval": 0}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "pairlists": [{"method": "StaticPairList"}], "logging": {"verbosity": 2, "logfile": "user_data/logs/leverage_optimization.log"}}