import logging
import warnings
from datetime import datetime, timedelta
from functools import reduce
from typing import TYPE_CHECKING

import pandas as pd
import pandas_ta as pta
import talib.abstract as ta
from freqtrade.strategy import BooleanParameter, DecimalParameter, IntParameter
from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame

if TYPE_CHECKING:
    from freqtrade.persistence import Trade

warnings.simplefilter(action="ignore", category=RuntimeWarning)

logger = logging.getLogger(__name__)


class E0V1E_Enhanced(IStrategy):
    """
    E0V1E 增强版多空策略

    策略核心思想：
    基于均值回归的5分钟短线多空策略，在多重技术指标确认的超卖/超买状态下入场，
    通过动态出场机制和严格的风险管理来获取短期反弹/回调收益。

    主要特性：
    1. 支持做多和做空双向交易
    2. 统一的入场逻辑，消除代码重复
    3. 优化的风险管理和动态止损
    4. 增强的技术指标和成交量确认
    5. 智能的出场逻辑和风险控制
    6. 市场环境过滤机制
    7. 全面参数化，支持多空独立优化
    """

    # === 基础配置 ===
    # Buy hyperspace params:
    buy_params = {
        "atr_max_pct": 5.4,
        "atr_min_pct": 1.0,
        "buy_cci_loss": -63,
        "buy_cti": -0.1,
        "buy_fastk_profit": 8,
        "buy_rsi": 26,
        "buy_rsi_alt": 20,
        "buy_rsi_fast": 38,
        "buy_rsi_fast_alt": 27,
        "buy_rsi_slow_trend": True,
        "buy_sma_ratio": 0.979,
        "buy_sma_ratio_alt": 0.976,
        "enable_alt_long_entry": True,
        "enable_atr_filter": True,
        "enable_long_trading": True,
        "enable_market_filter": True,
        "enable_volume_filter": True,
        "market_24h_change_max": 17.3,
        "market_24h_change_min": -15.3,
        "volume_factor": 1.0,
        # 动态杠杆调整参数
        "base_leverage_low_vol": 5.0,
        "base_leverage_normal": 3.0,
        "base_leverage_high_vol": 2.0,
        "atr_low_threshold": 1.5,
        "atr_high_threshold": 3.5,
        "main_signal_leverage_multiplier": 1.0,
        "alt_signal_leverage_multiplier": 0.33,
        "enable_rsi_leverage_boost": True,
        "rsi_extreme_threshold": 20,
        "rsi_leverage_boost_factor": 1.3,
        "enable_volume_leverage_adjustment": True,
        "volume_high_threshold": 3.0,
        "volume_leverage_reduction_factor": 0.8,
    }

    # Sell hyperspace params:
    sell_params = {
        "max_hold_hours": 11,
        "profit_protection_fastk_long": 75,
        "profit_protection_threshold": 0.08,
        "sell_cci_loss": 75,
        "sell_fastk_profit": 71,
        "sell_rsi_peak": 81,
        "time_exit_profit_threshold": -0.052,
        "sell_rsi_slow_trend": True,  # value loaded from strategy
    }

    minimal_roi = {}  # 禁用基于时间的ROI，完全依赖动态出场
    timeframe = "5m"
    process_only_new_candles = True
    startup_candle_count = 300

    # === 订单配置 ===
    order_types = {
        "entry": "market",
        "exit": "market",
        "emergency_exit": "market",
        "force_entry": "market",
        "force_exit": "market",
        "stoploss": "market",
        "stoploss_on_exchange": False,
        "stoploss_on_exchange_interval": 60,
        "stoploss_on_exchange_market_ratio": 0.99,
    }

    # === 风险管理配置 ===
    stoploss = -0.15  # 统一止损水平

    # 追踪止损配置
    trailing_stop = True
    trailing_stop_positive = 0.1  # 当价格从最高点回落0.3%时，触发追踪止盈
    trailing_stop_positive_offset = 0.3  # 利润达到3%后，才激活追踪止损
    trailing_only_offset_is_reached = True

    use_custom_stoploss = False

    # === 策略开关 ===
    enable_long_trading = BooleanParameter(default=True, space="buy", optimize=False)
    enable_market_filter = BooleanParameter(default=True, space="buy", optimize=False)
    enable_volume_filter = BooleanParameter(default=True, space="buy", optimize=False)
    enable_atr_filter = BooleanParameter(default=True, space="buy", optimize=False)

    # === 市场环境过滤参数 ===
    market_24h_change_min = DecimalParameter(
        -25.0, -5.0, default=-18.0, decimals=1, space="buy", optimize=True
    )
    market_24h_change_max = DecimalParameter(
        5.0, 30.0, default=20.0, decimals=1, space="buy", optimize=True
    )

    # ATR波动性过滤
    atr_min_pct = DecimalParameter(0.3, 1.0, default=0.5, decimals=1, space="buy", optimize=True)
    atr_max_pct = DecimalParameter(3.0, 8.0, default=5.0, decimals=1, space="buy", optimize=True)

    # 成交量过滤
    volume_factor = DecimalParameter(1.0, 2.0, default=1.2, decimals=1, space="buy", optimize=True)

    # === 做多入场信号参数 ===
    buy_rsi_fast = IntParameter(20, 50, default=35, space="buy", optimize=True)
    buy_rsi = IntParameter(15, 45, default=30, space="buy", optimize=True)
    buy_rsi_slow_trend = BooleanParameter(default=True, space="buy", optimize=False)
    buy_sma_ratio = DecimalParameter(
        0.92, 0.99, default=0.96, decimals=3, space="buy", optimize=True
    )
    buy_cti = DecimalParameter(-1.0, 0.5, default=-0.2, decimals=2, space="buy", optimize=True)

    # === 备用做多入场信号参数 ===
    enable_alt_long_entry = BooleanParameter(default=True, space="buy", optimize=False)
    buy_rsi_fast_alt = IntParameter(25, 45, default=34, space="buy", optimize=True)
    buy_rsi_alt = IntParameter(20, 35, default=28, space="buy", optimize=True)
    buy_sma_ratio_alt = DecimalParameter(
        0.94, 0.98, default=0.96, decimals=3, space="buy", optimize=True
    )

    # === 做多出场信号参数 ===
    sell_rsi_slow_trend = BooleanParameter(default=True, space="sell", optimize=False)
    sell_fastk_profit = IntParameter(70, 95, default=84, space="sell", optimize=True)
    sell_cci_loss = IntParameter(60, 100, default=80, space="sell", optimize=True)
    sell_rsi_peak = IntParameter(65, 85, default=75, space="sell", optimize=True)

    # 时间止损参数
    max_hold_hours = IntParameter(4, 12, default=8, space="sell", optimize=True)
    time_exit_profit_threshold = DecimalParameter(
        -0.08, -0.02, default=-0.05, decimals=3, space="sell", optimize=True
    )

    # 利润保护参数
    profit_protection_threshold = DecimalParameter(
        0.03, 0.08, default=0.05, decimals=3, space="sell", optimize=True
    )
    profit_protection_fastk_long = IntParameter(65, 85, default=75, space="sell", optimize=True)

    # === 动态杠杆调整超参数 ===
    # 基础杠杆设置
    base_leverage_low_vol = DecimalParameter(
        2.0, 6.0, default=5.0, decimals=1, space="buy", optimize=True
    )
    base_leverage_normal = DecimalParameter(
        2.0, 5.0, default=3.0, decimals=1, space="buy", optimize=True
    )
    base_leverage_high_vol = DecimalParameter(
        1.0, 3.0, default=2.0, decimals=1, space="buy", optimize=True
    )

    # 波动性阈值
    atr_low_threshold = DecimalParameter(
        1.0, 2.0, default=1.5, decimals=1, space="buy", optimize=True
    )
    atr_high_threshold = DecimalParameter(
        3.0, 5.0, default=3.5, decimals=1, space="buy", optimize=True
    )

    # 信号类型杠杆调整系数
    main_signal_leverage_multiplier = DecimalParameter(
        0.8, 1.2, default=1.0, decimals=2, space="buy", optimize=True
    )
    alt_signal_leverage_multiplier = DecimalParameter(
        0.2, 0.6, default=0.33, decimals=2, space="buy", optimize=True
    )

    # RSI极值杠杆调整
    enable_rsi_leverage_boost = BooleanParameter(default=True, space="buy", optimize=False)
    rsi_extreme_threshold = IntParameter(15, 25, default=20, space="buy", optimize=True)
    rsi_leverage_boost_factor = DecimalParameter(
        1.1, 1.5, default=1.3, decimals=2, space="buy", optimize=True
    )

    # 成交量杠杆调整
    enable_volume_leverage_adjustment = BooleanParameter(default=True, space="buy", optimize=False)
    volume_high_threshold = DecimalParameter(
        2.0, 4.0, default=3.0, decimals=1, space="buy", optimize=True
    )
    volume_leverage_reduction_factor = DecimalParameter(
        0.6, 0.9, default=0.8, decimals=2, space="buy", optimize=True
    )

    @property
    def protections(self):
        """交易保护机制"""
        return [
            # 保护机制 1: 交易冷却期
            {
                "method": "CooldownPeriod",
                # 任何交易结束后，该交易对冷却48根K线（48 * 5m = 240分钟 = 4小时）
                # 目的是避免在同一交易对上因市场短期震荡而产生连续的无效交易。
                "stop_duration_candles": 48,
            },
            # 保护机制 2: 止损后暂停交易
            {
                "method": "StoplossGuard",
                # 在最近24根K线 (24 * 5m = 2小时) 内回顾
                "lookback_period_candles": 24,
                # 如果有1笔交易触发了止损
                "trade_limit": 1,
                # 则暂停该交易对的买入信号，持续60根K线 (60 * 5m = 300分钟 = 5小时)
                "stop_duration_candles": 60,
                # 此规则仅应用于当前交易对
                "only_per_pair": True,
            },
        ]

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """计算技术指标"""

        # === RSI系列指标 ===
        dataframe["rsi"] = ta.RSI(dataframe, timeperiod=14)
        dataframe["rsi_fast"] = ta.RSI(dataframe, timeperiod=4)
        dataframe["rsi_slow"] = ta.RSI(dataframe, timeperiod=20)

        # === 移动平均线 ===
        dataframe["sma_15"] = ta.SMA(dataframe, timeperiod=15)
        dataframe["ema_20"] = ta.EMA(dataframe, timeperiod=20)

        # === 趋势和动量指标 ===
        dataframe["cti"] = pta.cti(dataframe["close"], length=20)
        dataframe["cci"] = ta.CCI(dataframe, timeperiod=20)

        # === 随机指标 ===
        stoch_fast = ta.STOCHF(dataframe, 5, 3, 0, 3, 0)
        dataframe["fastk"] = stoch_fast["fastk"]
        dataframe["fastd"] = stoch_fast["fastd"]

        # === 波动性指标 ===
        dataframe["atr"] = ta.ATR(dataframe, timeperiod=14)
        dataframe["atr_pct"] = (dataframe["atr"] / dataframe["close"]) * 100

        # === 成交量指标 ===
        dataframe["volume_sma"] = ta.SMA(dataframe["volume"], timeperiod=20)
        dataframe["volume_ratio"] = dataframe["volume"] / dataframe["volume_sma"]

        # === 市场环境指标 ===
        # 24小时价格变化百分比
        dataframe["24h_change_pct"] = (
            (dataframe["close"] - dataframe["close"].shift(288)) / dataframe["close"].shift(288)
        ) * 100

        # === 布林带指标 ===
        bollinger = ta.BBANDS(dataframe, timeperiod=20, nbdevup=2.0, nbdevdn=2.0, matype=0)
        dataframe["bb_lowerband"] = bollinger["lowerband"]
        dataframe["bb_middleband"] = bollinger["middleband"]
        dataframe["bb_upperband"] = bollinger["upperband"]
        dataframe["bb_percent"] = (dataframe["close"] - dataframe["bb_lowerband"]) / (
            dataframe["bb_upperband"] - dataframe["bb_lowerband"]
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """定义多空入场信号"""
        long_conditions = []
        dataframe.loc[:, "enter_tag"] = ""

        # === 市场环境过滤器 ===
        market_filter = True
        if self.enable_market_filter.value:
            market_filter = (
                (dataframe["24h_change_pct"].notna())
                & (dataframe["24h_change_pct"] > self.market_24h_change_min.value)
                & (dataframe["24h_change_pct"] < self.market_24h_change_max.value)
            )

        # === ATR波动性过滤器 ===
        atr_filter = True
        if self.enable_atr_filter.value:
            atr_filter = (dataframe["atr_pct"] > self.atr_min_pct.value) & (
                dataframe["atr_pct"] < self.atr_max_pct.value
            )

        # === 成交量过滤器 ===
        volume_filter = True
        if self.enable_volume_filter.value:
            volume_filter = dataframe["volume_ratio"] > self.volume_factor.value

        # === 基础过滤条件组合 ===
        base_filter = market_filter & atr_filter & volume_filter

        # === 做多入场信号 ===
        if self.enable_long_trading.value:
            # 主做多信号：统一的超卖反弹逻辑
            rsi_trend_condition = True
            if self.buy_rsi_slow_trend.value:
                rsi_trend_condition = dataframe["rsi_slow"] < dataframe["rsi_slow"].shift(1)

            main_long_entry = (
                base_filter
                & rsi_trend_condition
                & (dataframe["rsi_fast"] < self.buy_rsi_fast.value)
                & (dataframe["rsi"] > self.buy_rsi.value)
                & (dataframe["close"] < dataframe["sma_15"] * self.buy_sma_ratio.value)
                & (dataframe["cti"] < self.buy_cti.value)
                & (dataframe["bb_percent"] < 0.2)  # 接近布林带下轨
            )

            long_conditions.append(main_long_entry)
            dataframe.loc[main_long_entry, "enter_tag"] += "main_long"

            # 备用做多信号：更激进的抄底
            if self.enable_alt_long_entry.value:
                alt_long_entry = (
                    base_filter
                    & (dataframe["rsi_slow"] < dataframe["rsi_slow"].shift(1))
                    & (dataframe["rsi_fast"] < self.buy_rsi_fast_alt.value)
                    & (dataframe["rsi"] > self.buy_rsi_alt.value)
                    & (dataframe["close"] < dataframe["sma_15"] * self.buy_sma_ratio_alt.value)
                    & (dataframe["cti"] < self.buy_cti.value)
                    & (dataframe["close"] < dataframe["bb_lowerband"])  # 突破布林带下轨
                )

                long_conditions.append(alt_long_entry)
                dataframe.loc[alt_long_entry, "enter_tag"] += "alt_long"

        # === 应用入场条件 ===
        if long_conditions:
            dataframe.loc[reduce(lambda x, y: x | y, long_conditions), "enter_long"] = 1

        return dataframe

    def custom_exit(
        self,
        pair: str,
        trade: "Trade",
        current_time: "datetime",
        current_rate: float,
        current_profit: float,
        **kwargs,
    ):
        """
        动态多空出场逻辑
        根据不同的入场原因和交易方向实现智能出场
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()

        # === 时间止损检查 ===
        if current_time - trade.open_date_utc > timedelta(hours=self.max_hold_hours.value):
            if current_profit > self.time_exit_profit_threshold.value:
                return "exit_time_limit"

        # 盈利时的出场策略
        if current_profit > 0:
            # 高利润保护机制
            if current_profit > self.profit_protection_threshold.value:
                if current_candle["fastk"] > self.profit_protection_fastk_long.value:
                    return "高利润保护"

            # 标准盈利出场
            if current_candle["fastk"] > self.sell_fastk_profit.value:
                return "标准利润保护"

            # 针对主做多信号的特定出场
            if "main_long" in str(trade.enter_tag):
                if current_candle["rsi"] > self.sell_rsi_peak.value:
                    return "主做多信号保护"

            # 针对备用做多信号的特定出场
            if "alt_long" in str(trade.enter_tag):
                # 布林带回归中轨出场
                if current_rate > current_candle["bb_middleband"]:
                    return "布林带回归中轨"

        # 风险控制出场
        # 小幅亏损时的保护性出场
        if current_profit > -0.05:
            if current_candle["cci"] > self.sell_cci_loss.value:
                return "小幅亏损风险"

        # 趋势反转信号
        if current_profit > -0.03:
            if (
                current_candle["rsi"] > 70
                and current_candle["fastk"] > 80
                and current_candle["fastd"] > 75
            ):
                return "趋势反转信号"

        # 波动性异常退出
        if current_candle["atr_pct"] > self.atr_max_pct.value * 1.5:
            if current_profit > -0.08:
                return "波动性异常退出"

        # # 成交量异常退出
        # if current_candle["volume_ratio"] > 3.0:  # 异常放量
        #     if current_profit > -0.04:
        #         return "成交量异常退出"

        return None

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        静态出场信号（主要依赖custom_exit）
        """
        dataframe.loc[:, ["exit_long", "exit_tag"]] = (0, None)
        return dataframe

    def confirm_trade_entry(
        self,
        pair: str,
        order_type: str,
        amount: float,
        rate: float,
        time_in_force: str,
        current_time: datetime,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> bool:
        """
        交易确认函数 - 多空交易的最后一道风险控制
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return False

        current_candle = dataframe.iloc[-1].squeeze()

        # 确保关键指标存在且有效
        required_indicators = ["rsi", "rsi_fast", "atr_pct", "volume_ratio"]
        for indicator in required_indicators:
            if indicator not in current_candle or pd.isna(current_candle[indicator]):
                return False

        # 最终风险检查
        if current_candle["atr_pct"] > 10.0:  # 极端波动
            return False

        if current_candle["rsi"] < 10 or current_candle["rsi"] > 90:  # 极端RSI
            return False

        # 做多时确保不在极端超买状态
        if current_candle["rsi"] > 80 and current_candle["fastk"] > 90:
            return False

        return True

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        动态杠杆调整 - 基于超参数优化的智能杠杆策略

        杠杆调整逻辑：
        1. 根据市场波动性（ATR）调整基础杠杆
        2. 根据入场信号类型调整杠杆倍数
        3. 根据RSI极值情况进行杠杆增强
        4. 根据成交量异常情况降低杠杆
        5. 综合所有因素计算最终杠杆
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return 1.0

        current_candle = dataframe.iloc[-1].squeeze()

        # === 第一步：根据波动性确定基础杠杆 ===
        base_leverage = self.base_leverage_normal.value  # 默认正常波动杠杆

        if "atr_pct" in current_candle and not pd.isna(current_candle["atr_pct"]):
            atr_pct = current_candle["atr_pct"]

            if atr_pct > self.atr_high_threshold.value:
                # 高波动时使用低杠杆
                base_leverage = self.base_leverage_high_vol.value
            elif atr_pct < self.atr_low_threshold.value:
                # 低波动时可以使用高杠杆
                base_leverage = self.base_leverage_low_vol.value

        # === 第二步：根据入场信号类型调整杠杆 ===
        signal_multiplier = 1.0

        if "main_" in entry_tag:
            # 主信号使用标准杠杆倍数
            signal_multiplier = self.main_signal_leverage_multiplier.value
        elif "alt_" in entry_tag:
            # 备用信号使用较低杠杆倍数
            signal_multiplier = self.alt_signal_leverage_multiplier.value

        adjusted_leverage = base_leverage * signal_multiplier

        # === 第三步：RSI极值杠杆增强 ===
        if (
            self.enable_rsi_leverage_boost.value
            and "rsi" in current_candle
            and not pd.isna(current_candle["rsi"])
        ):
            rsi = current_candle["rsi"]

            # 做多时，RSI极度超卖可以增加杠杆
            if side == "long" and rsi < self.rsi_extreme_threshold.value:
                adjusted_leverage *= self.rsi_leverage_boost_factor.value

        # === 第四步：成交量异常杠杆调整 ===
        if (
            self.enable_volume_leverage_adjustment.value
            and "volume_ratio" in current_candle
            and not pd.isna(current_candle["volume_ratio"])
        ):
            volume_ratio = current_candle["volume_ratio"]

            # 异常放量时降低杠杆
            if volume_ratio > self.volume_high_threshold.value:
                adjusted_leverage *= self.volume_leverage_reduction_factor.value

        # === 第五步：最终杠杆限制和安全检查 ===
        # 确保杠杆在合理范围内
        final_leverage = max(1.0, min(adjusted_leverage, max_leverage))

        # 额外的安全检查
        if "atr_pct" in current_candle and current_candle["atr_pct"] > 8.0:
            # 极端波动时强制降低杠杆
            final_leverage = min(final_leverage, 1.5)

        return final_leverage

    def custom_stake_amount(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_stake: float,
        min_stake: float,
        max_stake: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        动态仓位管理 - 支持多空不同仓位策略
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if dataframe is None or len(dataframe) < 1:
            return proposed_stake

        current_candle = dataframe.iloc[-1].squeeze()

        # 基础仓位
        base_stake = proposed_stake

        # 根据信号强度调整仓位
        if "main_" in entry_tag:
            # 主信号使用标准仓位
            position_multiplier = 1.0
        elif "alt_" in entry_tag:
            # 备用信号使用较小仓位
            position_multiplier = 0.7
        else:
            position_multiplier = 0.8

        # 根据市场波动性调整仓位
        if "atr_pct" in current_candle:
            atr_pct = current_candle["atr_pct"]
            if atr_pct > 5.0:
                position_multiplier *= 1  # 高波动时减小仓位
            elif atr_pct < 1.0:
                position_multiplier *= 2  # 低波动时可以适当增加仓位

        # 根据RSI极值调整仓位
        if "rsi" in current_candle:
            rsi = current_candle["rsi"]
            if side == "long" and rsi < 20:
                position_multiplier *= 1.2  # 极度超卖时做多可以稍微增加仓位

        final_stake = base_stake * position_multiplier
        return max(min_stake, min(final_stake, max_stake))
